<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.MaterialReceiveLogMapper">
    
    <resultMap type="MaterialReceiveLog" id="MaterialReceiveLogResult">
        <result property="recordId"    column="record_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="materialId"    column="material_id"    />
        <result property="materialName"    column="material_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="status"    column="status"    />
        <result property="triggerType"    column="trigger_type"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="handlerId"    column="handler_id"    />
        <result property="handlerName"    column="handler_name"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialReceiveLogVo">
        select record_id, user_id, user_name, material_id, material_name, quantity, status, trigger_type, apply_time, confirm_time, handler_id, handler_name, remark, create_time, update_time from material_receive_log
    </sql>

    <select id="selectMaterialReceiveLogList" parameterType="MaterialReceiveLog" resultMap="MaterialReceiveLogResult">
        <include refid="selectMaterialReceiveLogVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="triggerType != null  and triggerType != ''"> and trigger_type = #{triggerType}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="confirmTime != null "> and confirm_time = #{confirmTime}</if>
        </where>
        order by apply_time asc, 
    </select>
    
    <select id="selectMaterialReceiveLogByRecordId" parameterType="Long" resultMap="MaterialReceiveLogResult">
        <include refid="selectMaterialReceiveLogVo"/>
        where record_id = #{recordId}
    </select>

    <insert id="insertMaterialReceiveLog" parameterType="MaterialReceiveLog" useGeneratedKeys="true" keyProperty="recordId">
        insert into material_receive_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="materialId != null">material_id,</if>
            <if test="materialName != null">material_name,</if>
            <if test="quantity != null">quantity,</if>
            <if test="status != null">status,</if>
            <if test="triggerType != null">trigger_type,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="handlerId != null">handler_id,</if>
            <if test="handlerName != null">handler_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="status != null">#{status},</if>
            <if test="triggerType != null">#{triggerType},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="handlerId != null">#{handlerId},</if>
            <if test="handlerName != null">#{handlerName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialReceiveLog" parameterType="MaterialReceiveLog">
        update material_receive_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="materialId != null">material_id = #{materialId},</if>
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="status != null">status = #{status},</if>
            <if test="triggerType != null">trigger_type = #{triggerType},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="handlerId != null">handler_id = #{handlerId},</if>
            <if test="handlerName != null">handler_name = #{handlerName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteMaterialReceiveLogByRecordId" parameterType="Long">
        delete from material_receive_log where record_id = #{recordId}
    </delete>

    <delete id="deleteMaterialReceiveLogByRecordIds" parameterType="String">
        delete from material_receive_log where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>
